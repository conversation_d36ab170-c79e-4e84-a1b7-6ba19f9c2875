<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx">
        <testCase name="RAAproval renders loading state" duration="65"/>
        <testCase name="RAAproval renders error state" duration="13"/>
        <testCase name="RAAproval renders no data state" duration="24"/>
        <testCase name="RAAproval handles Cancel button" duration="10"/>
        <testCase name="RAAproval handles template fetch error" duration="3"/>
        <testCase name="RAAproval additional coverage calls updateSavedRA on Save button click" duration="6"/>
        <testCase name="RAAproval additional coverage shows Level of RA dropdown and handles Save for non-ROUTINE" duration="8"/>
        <testCase name="RAAproval additional coverage handles categories/hazards not matching template" duration="3"/>
        <testCase name="RAAproval additional coverage handles ROUTINE level with modal confirm" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel error case" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles generatePDF success case" duration="5">
            <failure message="Error: API Error"><![CDATA[Error: API Error
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:368:46)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles generatePDF error case" duration="7"/>
        <testCase name="RAAproval comprehensive coverage handles updateRaDetails error case" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles dropdown onChange with empty value" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles Cancel button in preview mode" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles Cancel button in edit mode (shows exit modal)" duration="1004">
            <failure message="Error: expect(received).toBeInTheDocument()"><![CDATA[Error: expect(received).toBeInTheDocument()

received value must be an HTMLElement or an SVGElement.
Received has value: null

Ignored nodes: comments, script, style
<html>
  <head />
  <body>
    <div>
      <div
        class="ra-approval-page"
      >
        <div
          data-testid="preview-form-details"
        >
          <div
            class="d-flex align-items-center"
          />
          <button>
            Cancel
          </button>
          <button>
            Save
          </button>
        </div>
      </div>
    </div>
  </body>
</html>
    at __EXTERNAL_MATCHER_TRAP__ (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/expect/build/index.js:386:30)
    at Object.toBeInTheDocument (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/expect/build/index.js:387:15)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:532:55
    at runWithExpensiveErrorDiagnosticsDisabled (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/config.js:47:12)
    at checkCallback (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/wait-for.js:127:77)
    at checkRealTimersCallback (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/wait-for.js:121:16)
    at Timeout.task [as _onTimeout] (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jsdom/lib/jsdom/browser/Window.js:516:19)
    at listOnTimeout (node:internal/timers:573:17)
    at processTimers (node:internal/timers:514:7)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles ExitPageModal onClose" duration="4">
            <failure message="TestingLibraryElementError: Unable to find an element by: [data-testid=&quot;exit-page-modal&quot;]"><![CDATA[TestingLibraryElementError: Unable to find an element by: [data-testid="exit-page-modal"]

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="ra-approval-page"
    >
      <div
        data-testid="preview-form-details"
      >
        <div
          class="d-flex align-items-center"
        />
        <button>
          Cancel
        </button>
        <button>
          Save
        </button>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/config.js:37:19)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:76:38
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:52:17
    at getByTestId (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:95:19)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:566:30)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles ExitPageModal onConfirm" duration="3">
            <failure message="TestingLibraryElementError: Unable to find an element by: [data-testid=&quot;exit-page-modal&quot;]"><![CDATA[TestingLibraryElementError: Unable to find an element by: [data-testid="exit-page-modal"]

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="ra-approval-page"
    >
      <div
        data-testid="preview-form-details"
      >
        <div
          class="d-flex align-items-center"
        />
        <button>
          Cancel
        </button>
        <button>
          Save
        </button>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/config.js:37:19)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:76:38
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:52:17
    at getByTestId (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/dom/dist/query-helpers.js:95:19)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:604:30)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles breadcrumb click when blocking" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles beforeunload event when blocking" duration="3">
            <failure message="Error: expect(jest.fn()).toHaveBeenCalled()"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalled()

Expected number of calls: >= 1
Received number of calls:    0
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:680:46)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with missing parameters" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles SubmitRoutineRAModal onConfirm" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with invalid parameters (no levelOfRA)" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE but no actionDate" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with text field changes" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with assessment changes" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles previewOnly calculation with different user scenarios" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles window beforeunload event properly" duration="2"/>
    </file>
</testExecutions>