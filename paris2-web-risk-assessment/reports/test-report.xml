<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx">
        <testCase name="RAAproval renders loading state" duration="28"/>
        <testCase name="RAAproval renders error state" duration="5"/>
        <testCase name="RAAproval renders no data state" duration="4"/>
        <testCase name="RAAproval handles Cancel button" duration="4"/>
        <testCase name="RAAproval handles template fetch error" duration="2"/>
        <testCase name="RAAproval additional coverage calls updateSavedRA on Save button click" duration="5"/>
        <testCase name="RAAproval additional coverage shows Level of RA dropdown and handles Save for non-ROUTINE" duration="7"/>
        <testCase name="RAAproval additional coverage handles categories/hazards not matching template" duration="2"/>
        <testCase name="RAAproval additional coverage handles ROUTINE level with modal confirm" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel error case" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles generatePDF success case" duration="5">
            <failure message="Error: API Error"><![CDATA[Error: API Error
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx:379:46)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RAAproval comprehensive coverage handles generatePDF error case" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles updateRaDetails error case" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles dropdown onChange with empty value" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles Cancel button in preview mode" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles beforeunload event listener setup and cleanup" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with different data scenarios" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with template data mismatch" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE level and action date" duration="1"/>
        <testCase name="RAAproval comprehensive coverage handles form data processing with different field types" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with missing parameters" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles ROUTINE level selection and save button state" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with invalid parameters (no levelOfRA)" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE but no actionDate" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with text field changes" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with assessment changes" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles previewOnly calculation with different user scenarios" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles window beforeunload event properly" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles different approval status scenarios" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles edge case with empty risk_approver array" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles risk parameter data processing with complex data" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with null/undefined parameter types" duration="1"/>
        <testCase name="RAAproval comprehensive coverage handles setDataStore call in loadBasicDetails" duration="1"/>
    </file>
</testExecutions>